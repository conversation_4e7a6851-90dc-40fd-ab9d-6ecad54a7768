<?php

namespace App\Livewire;

use App\Models\Cotizacion;
use App\Models\Vehicle\VehicleMake;
use App\Models\Vehicle\VehicleModel;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Livewire\Component;

class EstimateVehicle extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('marca')
                    ->label('Marca')
                    ->options(VehicleMake::pluck('name', 'id'))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->live()
                    ->placeholder('Selecciona una Marca'),

                Select::make('modelo')
                    ->label('Modelo')
                    ->options(function (Get $get) {
                        $makeId = $get('marca');
                        if (! $makeId) {
                            return [];
                        }

                        return VehicleModel::where('vehicle_make_id', $makeId)
                            ->pluck('name', 'id');
                    })
                    ->searchable()
                    ->required()
                    ->placeholder('Selecciona un modelo')
                    ->disabled(fn (Get $get) => ! $get('marca')),

                TextInput::make('ano')
                    ->label('Año')
                    ->numeric()
                    ->required()
                    ->minValue(1900)
                    ->maxValue(date('Y')),

                TextInput::make('suma')
                    ->label('Suma Asegurada')
                    ->numeric()
                    ->required()
                    ->prefix('$'),

                Select::make('plan')
                    ->label('Plan')
                    ->options([
                        'Mensual full' => 'Mensual Full',
                        'Anual full' => 'Anual Full',
                    ])
                    ->default('Mensual full')
                    ->required(),

                Select::make('uso')
                    ->label('Uso')
                    ->options([
                        'Privado' => 'Privado',
                        'Publico' => 'Público',
                    ])
                    ->default('Privado')
                    ->required(),

                Select::make('estado')
                    ->label('Estado')
                    ->options([
                        'Nuevo' => 'Nuevo',
                        'Usado' => 'Usado',
                    ])
                    ->default('Nuevo')
                    ->required(),

                Checkbox::make('salvamento')
                    ->label('Salvamento')
                    ->inline(false),
            ])
            ->statePath('data')
            ->columns();
    }

    public function create(): void
    {
        $data = $this->form->getState();

        try {
            // Crear la cotización con los datos del vehículo
            $cotizacion = Cotizacion::create([
                'marca_id' => $data['marca'],
                'modelo_id' => $data['modelo'],
                'ano' => $data['ano'],
                'suma_asegurada' => $data['suma'],
                'plan' => $data['plan'],
                'uso' => $data['uso'],
                'estado' => $data['estado'],
                'salvamento' => $data['salvamento'] ?? false,
                'status' => 'pending',
            ]);

            Notification::make()
                ->title('Cotización creada exitosamente')
                ->success()
                ->body('Ahora puedes completar los datos del cliente.')
                ->send();

            // Redirigir a la página de completar cotización
            $this->redirect(route('cotizacion.completar', $cotizacion->id));

        } catch (\Exception $e) {
            Notification::make()
                ->title('Error al crear la cotización')
                ->danger()
                ->body('Por favor, intenta nuevamente.')
                ->send();
        }
    }

    public function submit(): void
    {
        $this->create();
    }

    public function render()
    {
        return view('livewire.estimate-vehicle');
    }
}
