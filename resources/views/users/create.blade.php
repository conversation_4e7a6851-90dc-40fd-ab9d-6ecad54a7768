<x-templates.app>
    <div class="container-fluid px-4">
        <h1 class="mt-4">Editar Usuario</h1>
        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-user-edit me-1"></i>
                Editar detalles del usuario
            </div>

            <div class="card-body">
                <form method="POST" action="{{ route('users.store') }}">
                    @csrf

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">Nombre de usuario</label>

                                <input type="text"
                                       class="form-control @error('username') is-invalid @enderror"
                                       id="username"
                                       name="username"
                                       value="{{ old('username') }}"/>

                                @error('username')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>


                            <div class="mb-3">
                                <label for="name" class="form-label">Nombre</label>

                                <input type="text"
                                       class="form-control @error('name') is-invalid @enderror"
                                       id="name"
                                       name="name"
                                       value="{{ old('name') }}"/>

                                @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>


                            {{--                        <div class="mb-3">--}}

                            {{--                            <label for="role_id" class="form-label">Rol</label>--}}

                            {{--                            <select class="form-select {{ session('errors.role_id') ? 'is-invalid' : '' }}"--}}

                            {{--                                    id="role_id"--}}

                            {{--                                    name="role_id" required>--}}

                            {{--                                <option value="">Seleccione un rol</option>--}}

                            {{--                                <?php foreach ($roles as $role): }}--}}

                            {{--                                <option value="{{ $role['id'] }}"--}}

                            {{--                                        {{ old('role_id', $user['role_id'] ?? '') == $role['id'] ? 'selected' : '' }}>--}}

                            {{--                                        {{ esc($role['name']) }}--}}

                            {{--                                </option>--}}

                            {{--                                <?php endforeach; }}--}}

                            {{--                            </select>--}}

                            {{--                            <?php if (session('errors.role_id')) : }}--}}

                            {{--                            <div class="invalid-feedback">--}}

                            {{--                                    {{ esc(session('errors.role_id')) }}--}}

                            {{--                            </div>--}}

                            {{--                            <?php endif; }}--}}

                            {{--                        </div>--}}

                        </div>


                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Correo Electrónico</label>

                                <input type="email"
                                       class="form-control @error('email') is-invalid @enderror"
                                       id="email"
                                       name="email"
                                       value="{{ old('email') }}"/>

                                @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end mt-3">
                        <a href="{{ route('users.index') }}" class="btn btn-secondary me-2">Cancelar</a>
                        <button type="submit" class="btn btn-primary">Crear</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-templates.app>
