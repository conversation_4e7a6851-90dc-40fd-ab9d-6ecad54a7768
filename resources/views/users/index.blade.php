<x-templates.app>
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between align-items-center mt-4">
            <h1>Usuarios</h1>
            <a href="{{ route('users.create') }}" class="btn btn-success">
                <i class="fas fa-user-plus"></i> Crear Usuario
            </a>
        </div>

        @session('message')
        <div class="alert alert-danger">
            {{ $value }}
        </div>

        <br/>
        @endsession

        <div class="card mb-4">
            <div class="card-header">
                <i class="fas fa-table me-1"></i>
                Lista de Usuarios
            </div>
            <div class="card-body">
                <table id="datatablesSimple" class="table table-striped table-bordered">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nombre</th>
                        <th>Correo Electrónico</th>
                        <th>Nombre de usuario</th>
                        <th>Acciones</th>
                    </tr>
                    </thead>
                    <tbody>
                    @forelse ($users as $user)
                        <tr>
                            <td>{{ $user->id }}</td>
                            <td>{{ $user->name }}</td>
                            <td>{{ $user->email }}</td>
                            <td>{{ $user->username }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-primary btn-sm"
                                            onclick="editUser('{{ $user->id }}')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    |
                                    <form action="{{ route('users.destroy', $user->id) }}" method="post"
                                          style="display: inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger btn-sm"
                                                onclick="return confirm('¿Está seguro de que desea eliminar este usuario?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    |
                                    <form action="{{ route('users.reset-password', $user->id) }}" method="post"
                                          style="display: inline;">
                                        @csrf
                                        @method('PUT')
                                        <button type="submit" class="btn btn-warning btn-sm"
                                                onclick="return confirm('¿Está seguro de que desea resetear la contraseña de este usuario?')">
                                            <i class="fas fa-key"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="text-center">No se encontraron usuarios</td>
                        </tr>
                    @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function editUser(id) {
                window.location.href = "{{ route('users.edit', ':id') }}".replace(':id', id);
            }

            document.addEventListener('DOMContentLoaded', function () {
                const datatablesSimple = document.getElementById('datatablesSimple');
                if (datatablesSimple) {
                    new simpleDatatables.DataTable(datatablesSimple);
                }
            });
        </script>
    @endpush
</x-templates.app>
