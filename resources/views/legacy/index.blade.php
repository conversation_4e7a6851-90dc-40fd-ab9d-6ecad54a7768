<x-templates.app>

    <div class="card-group">
        <?php if (!empty($plan["Auto"])) : ?>
        <div class="card text-center">
            <div class="card-header">
                AUTO
            </div>
            <div class="card-body">
                <img src="<?= url('img/auto.png') ?>" height="200" width="150">
                <a class="stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#cotizar_auto"></a>
            </div>
        </div>
        <?php endif ?>

        <?php if (!empty($plan["Vida"])) : ?>
        <div class="card text-center">
            <div class="card-header">
                VIDA
            </div>
            <div class="card-body">
                <img src="<?= url('img/vida.png') ?>" height="200" width="150">
                <a class="stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#cotizar_vida"></a>
            </div>
        </div>
        <?php endif ?>

        <?php if (!empty($plan["Desempleo"])) : ?>
        <div class="card text-center">
            <div class="card-header">
                VIDA/DESEMPLEO
            </div>
            <div class="card-body">
                <img src="<?= url('img/desempleo.png') ?>" height="200" width="150">
                <a class="stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#cotizar_desempleo"></a>
            </div>
        </div>
        <?php endif ?>

        <?php if (!empty($plan["Incendio"])) : ?>
        <div class="card text-center">
            <div class="card-header">
                SEGURO INCENDIO HIPOTECARIO
            </div>
            <div class="card-body">
                <img src="<?= url('img/incendio.png') ?>" height="200" width="150">
                <a class="stretched-link" href="#" data-bs-toggle="modal" data-bs-target="#cotizar_incendio"></a>
            </div>
        </div>
        <?php endif ?>

        <?php if (!empty($plan["Leasing"])) : ?>
        <div class="card text-center">
            <div class="card-header">
                SEGURO INCENDIO LEASING
            </div>
            <div class="card-body">
                <img src="<?= url('img/incendio 2.png') ?>" height="200" width="150">
                <a class="stretched-link" href="#" data-bs-toggle="modal"
                   data-bs-target="#cotizar_incendio_leasing"></a>
            </div>
        </div>
        <?php endif ?>
    </div>

    @push('modals')
        @if(!empty($cotizacion->planes))
            @include('legacy.modals.tabla_resultados')
            @include('legacy.modals.completar_cotizacion')
        @endif

            @include('legacy.auto.cotizar')
            @include('legacy.vida.cotizar')
            @include('legacy.desempleo.cotizar')
            @include('legacy.incendio.cotizar')
            @include('legacy.incendio.cotizar_leasing')
    @endpush

    @push('scripts')
        <script>
            // Wait for DOM to be fully loaded
            document.addEventListener('DOMContentLoaded', function() {
                // Check if elements exist before creating modals
                const tablaResultadosElement = document.getElementById('tabla_resultados');
                const completarCotizacionElement = document.getElementById('completar_cotizacion');

                let tabla_resultados = null;
                let completar_cotizacion = null;

                // Only create modals if elements exist
                if (tablaResultadosElement) {
                    tabla_resultados = new bootstrap.Modal(tablaResultadosElement, {});
                    // Only show if modal was created successfully
                    tabla_resultados.show();
                }

                if (completarCotizacionElement) {
                    completar_cotizacion = new bootstrap.Modal(completarCotizacionElement, {});
                }

                // Make cerrar function available globally
                window.cerrar = function() {
                    if (tabla_resultados) {
                        tabla_resultados.hide();
                    }
                    if (completar_cotizacion) {
                        completar_cotizacion.show();
                    }
                };
            });

            //Funcion para cargar una url con codigo php cuando hagan una solicitud con ajax
            function modelosAJAX(val) {
                $.ajax({
                    type: 'ajax',
                    url: "<?= url('cotizaciones/lista_modelos') ?>",
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    method: "POST",
                    data: {
                        marcaid: val.value
                    },
                    success: function (response) {
                        //agrega el codigo php en el select
                        document.getElementById("modelos").innerHTML = response;
                        //refresca solo el select para actualizar la interfaz del select
                        $('.selectpicker').selectpicker('refresh');
                    },
                    error: function (data) {
                        console.log(data);
                    }
                });
            }
        </script>

        <script
            src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta2/dist/js/bootstrap-select.min.js"></script>
    @endpush

    @push('css')
        <link rel="stylesheet"
              href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta2/dist/css/bootstrap-select.min.css">

        <style>
            #fecha::-webkit-calendar-picker-indicator {
                padding-left: 50%;
            }

            #deudor::-webkit-calendar-picker-indicator {
                padding-left: 50%;
            }

            #codeudor::-webkit-calendar-picker-indicator {
                padding-left: 50%;
            }
        </style>
    @endpush
</x-templates.app>
